"use client";

import React, { useState, useEffect } from "react";
import { useMutateApiMutation } from "../../../redux/services/api";
import { useDispatch } from 'react-redux';
import { loginStart, loginSuccess, loginFailure } from '@/redux/slices/authSlice';
import GoBack from "@/components/backend/GoBack";
import Image from "next/image";
import Button from "@/components/backend/Button";
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import toast from 'react-hot-toast';

const Register = () => {
  const [acceptedTerms, setAcceptedTerms] = useState(false);
  const [formData, setFormData] = useState({
    username: "",
    email: "",
    password: "",
    business_name: "" // For sellers only
  });
  const [errors, setErrors] = useState({});
  const [showPassword, setShowPassword] = useState(false);
  const [userType, setUserType] = useState("personal"); // personal or business
  const [role, setRole] = useState("Buyer"); // Buyer or Seller
  const [showAgeConfirmation, setShowAgeConfirmation] = useState(true);
  const [isAgeConfirmed, setIsAgeConfirmed] = useState(false);

  const router = useRouter();
  const dispatch = useDispatch();
  const [mutateApi, { data: mutationData, error: mutationError, isLoading }] = useMutateApiMutation();

  useEffect(() => {
    if (mutationData) {
      console.log("Mutation API Response:", mutationData);
      const { accessToken, refreshToken, user } = mutationData.data;

      // Dispatch login start
      dispatch(loginStart());

      // Store tokens in Redux state
      dispatch(loginSuccess({ accessToken, refreshToken }));

      // Only store user data in cookies
      document.cookie = `user=${JSON.stringify(user)};path=/;max-age=31536000`;

      // Store subscription data if available
      if (mutationData?.data?.subscription) {
        document.cookie = `subscription=${JSON.stringify(mutationData?.data?.subscription)};path=/;max-age=31536000`;
      }

      // Check if profile is incomplete using is_complete field or fallback to name fields
      const isIncompleteProfile = user.is_complete === false ||
        (!user.first_name || !user.last_name);

      // Add a small delay to ensure cookies are set before navigation
      setTimeout(() => {
        if (isIncompleteProfile) {
          toast.success('Registration successful! Please complete your profile...');
          router.replace("/complete-profile");
        } else {
          toast.success('Registration successful! Redirecting to dashboard...');
          router.replace("/dashboard");
        }
      }, 100); // 100ms delay to ensure cookies are processed
    }
    if (mutationError) {
      // Dispatch login failure
      dispatch(loginFailure(mutationError.data?.message || "Registration failed"));

      // Use a safer way to log the error
      console.log("Mutation API Error:", JSON.stringify(mutationError, null, 2));

      // Handle different error response structures
      if (mutationError.data?.error) {
        // Handle the new error structure: { success: false, message: "Validation failed", error: { email: ["Email is already in use."] } }
        const serverErrors = {};
        Object.keys(mutationError.data.error).forEach(field => {
          const fieldErrors = mutationError.data.error[field];
          if (Array.isArray(fieldErrors)) {
            serverErrors[field] = fieldErrors[0]; // Take the first error message
          } else {
            serverErrors[field] = fieldErrors;
          }
        });
        setErrors(serverErrors);

        // Show the first error message in a toast
        const firstErrorKey = Object.keys(serverErrors)[0];
        if (firstErrorKey) {
          toast.error(serverErrors[firstErrorKey]);
        }
      } else if (mutationError.data?.errors) {
        // Handle the old error structure
        setErrors(mutationError.data.errors);
        // Show the first error message in a toast
        const firstErrorKey = Object.keys(mutationError.data.errors)[0];
        if (firstErrorKey) {
          toast.error(mutationError.data.errors[firstErrorKey]);
        }
      } else if (mutationError.data?.message) {
        setErrors({ server: mutationError.data.message });
        toast.error(mutationError.data.message);
      } else {
        setErrors({ server: "An error occurred during registration. Please try again." });
        toast.error("An error occurred during registration. Please try again.");
      }
    }
  }, [mutationData, mutationError, router, dispatch]); // Added router and dispatch to dependencies

  const validateForm = () => {
    let newErrors = {};

    if (!formData.username.trim()) {
      newErrors.username = "Username is required";
    }

    // Business name validation for business users only
    if (userType === "business") {
      if (!formData.business_name.trim()) {
        newErrors.business_name = "Business name is required for business accounts";
      }
    }

    if (!formData.email.trim()) {
      newErrors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Enter a valid email address";
    }

    if (!formData.password) {
      newErrors.password = "Password is required";
    } else {
      const password = formData.password;
      const passwordErrors = [];

      // Check minimum length (8 characters)
      if (password.length < 8) {
        passwordErrors.push("at least 8 characters");
      }

      // Check for lowercase letter
      if (!/[a-z]/.test(password)) {
        passwordErrors.push("1 lowercase letter");
      }

      // Check for uppercase letter
      if (!/[A-Z]/.test(password)) {
        passwordErrors.push("1 uppercase letter");
      }

      // Check for number
      if (!/[0-9]/.test(password)) {
        passwordErrors.push("1 number (0-9)");
      }

      // Check for special character
      if (!/[!@#$%^&*]/.test(password)) {
        passwordErrors.push("1 special character (!@#$%^&*)");
      }

      if (passwordErrors.length > 0) {
        newErrors.password = `Password must contain ${passwordErrors.join(", ")}`;
      }
    }

    if (!acceptedTerms) {
      newErrors.terms = "Please accept terms and conditions";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Clear any previous errors
    setErrors({});

    if (validateForm()) {
      const registrationData = {
        username: formData.username.trim(),
        password: formData.password,
        user_type: userType, // "personal" or "business"
        role: role, // "Buyer" or "Seller"
        age_confirm: isAgeConfirmed,
        email: formData.email.trim()
      };

      // Add business_name for business users (required field)
      if (userType === "business") {
        registrationData.business_name = formData.business_name.trim();
      }

      try {
        await mutateApi({
          endpoint: "/auth/new-register",
          data: registrationData
        });
      } catch (error) {
        // Use a safer way to log the error
        console.log("Registration error:", JSON.stringify(error, null, 2));
      }
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear field-specific error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: undefined }));
    }

    // Also clear server error when user modifies any field
    if (errors.server) {
      setErrors(prev => ({ ...prev, server: undefined }));
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  // Age confirmation handlers
  const handleAgeConfirmYes = () => {
    setIsAgeConfirmed(true);
    setShowAgeConfirmation(false);
  };

  const handleAgeConfirmNo = () => {
    setIsAgeConfirmed(false);
    setShowAgeConfirmation(false);
    toast.error("You must be 18 or older to register");
    router.push("/"); // Redirect to home page
  };

  return (
    <div className="flex min-h-screen">
      {/* Age Confirmation Popup */}
      {showAgeConfirmation && (
        <div className="fixed inset-0 backdrop-blur-sm bg-black/70 flex items-center justify-center z-50 transition-opacity duration-300">
          <div className="bg-white rounded-lg p-8 max-w-md mx-4 shadow-2xl animate-fadeIn">
            <div className="text-center">
              {/* Icon */}
              <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-blue-100 mb-4">
                <svg
                  className="h-8 w-8 text-blue-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>

              {/* Title */}
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                Age Verification
              </h3>

              {/* Message */}
              <p className="text-gray-600 mb-8 text-lg">
                Are you 18 years of age or older?
              </p>

              {/* Buttons */}
              <div className="flex gap-4 justify-center">
                <button
                  onClick={handleAgeConfirmYes}
                  className="px-8 py-3 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                >
                  Yes, I'm 18+
                </button>
                <button
                  onClick={handleAgeConfirmNo}
                  className="px-8 py-3 bg-gray-300 hover:bg-gray-400 text-gray-700 font-semibold rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
                >
                  No, I'm under 18
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Left Side - Form */}
      <div className="w-full lg:w-1/2 p-8 flex flex-col">
        <div className="max-w-md mx-auto w-full">
          {/* Back Button */}
          <div className="mb-16">
            <GoBack />
          </div>

          {/* Show form only if age is confirmed */}
          {isAgeConfirmed && (
            <>

          {/* Logo */}
          <div className="mb-8">
            <Image
              src="/assets/backend_assets/images/site-logo.svg"
              alt="logo"
              width={100}
              height={100}
              className="w-[200px]"
              priority
            />
          </div>

          <h2 className="text-4xl inter font-bold text-gray-800 mb-2">
            Sign up now
          </h2>
          <p className="text-gray-600 mt-4 mb-4 poppins">
            Create your account to get started
          </p>

          {/* Server Error Message */}
          {errors.server && (
            <div className="mb-6 p-4 bg-red-50 border border-red-200 text-red-700 rounded-md flex items-start">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 mr-2 mt-0.5 flex-shrink-0"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                  clipRule="evenodd"
                />
              </svg>
              <span>{errors.server}</span>
            </div>
          )}





          <form onSubmit={handleSubmit}>


            {/* Role Selection */}
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-3">Who are you?</h3>
              <div className="flex gap-4">
                <div className="flex-1">
                  <label
                    htmlFor="buyerRole"
                    className={`flex items-center justify-center block w-full py-3 px-4 rounded-md cursor-pointer transition ${
                      role === "Buyer"
                        ? 'bg-green-500 text-white'
                        : 'bg-gray-100 text-gray-700 border border-gray-300'
                    }`}
                  >
                    <input
                      type="radio"
                      id="buyerRole"
                      name="role"
                      value="Buyer"
                      checked={role === "Buyer"}
                      onChange={() => setRole("Buyer")}
                      className="hidden"
                    />
                    I'm a Buyer
                  </label>
                </div>
                <div className="flex-1">
                  <label
                    htmlFor="sellerRole"
                    className={`flex items-center justify-center block w-full py-3 px-4 rounded-md cursor-pointer transition ${
                      role === "Seller"
                        ? 'bg-green-500 text-white'
                        : 'bg-gray-100 text-gray-700 border border-gray-300'
                    }`}
                  >
                    <input
                      type="radio"
                      id="sellerRole"
                      name="role"
                      value="Seller"
                      checked={role === "Seller"}
                      onChange={() => setRole("Seller")}
                      className="hidden"
                    />
                    I'm a Seller
                  </label>
                </div>
              </div>
            </div>


            {/* Username */}
            <div className="mb-4">
              <div className={`flex items-center border ${
                errors.username ? 'border-red-500' : 'border-gray-300'
              } rounded-md`}>
                <div className="pl-4 pr-2">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5 text-gray-700"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                    />
                  </svg>
                </div>
                <input
                  type="text"
                  name="username"
                  className="w-full py-3 px-2 text-gray-700 focus:outline-none"
                  placeholder="Username"
                  value={formData.username}
                  onChange={handleChange}
                />
              </div>
              {errors.username && (
                <p className="text-red-500 text-sm mt-1">{errors.username}</p>
              )}
            </div>



            {/* Email */}
            <div className="mb-4">
              <div className={`flex items-center border ${
                errors.email ? 'border-red-500' : 'border-gray-300'
              } rounded-md`}>
                <div className="pl-4 pr-2">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5 text-gray-700"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                    />
                  </svg>
                </div>
                <input
                  type="email"
                  name="email"
                  className="w-full py-3 px-2 text-gray-700 focus:outline-none"
                  placeholder="Email"
                  value={formData.email}
                  onChange={handleChange}
                />
              </div>
              {errors.email && (
                <p className="text-red-500 text-sm mt-1">{errors.email}</p>
              )}
            </div>



            {/* Password */}
            <div className="mb-4">
              <div className={`flex items-center border ${
                errors.password ? 'border-red-500' : 'border-gray-300'
              } relative rounded-md`}>
                <div className="pl-4 pr-2">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5 text-gray-700"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                    />
                  </svg>
                </div>
                <input
                  type={showPassword ? "text" : "password"}
                  name="password"
                  className="w-full py-3 px-2 text-gray-700 focus:outline-none"
                  placeholder="Password"
                  value={formData.password}
                  onChange={handleChange}
                />
                <button
                  type="button"
                  onClick={togglePasswordVisibility}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer text-gray-600"
                  aria-label={showPassword ? "Hide password" : "Show password"}
                >
                  {showPassword ? (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21"
                      />
                    </svg>
                  ) : (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                      />
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                      />
                    </svg>
                  )}
                </button>
              </div>
              {errors.password && (
                <p className="text-red-500 text-sm mt-1">{errors.password}</p>
              )}

              {/* Password Requirements */}
              {formData.password && (
                <div className="mt-2 p-3 bg-gray-50 rounded-md">
                  <p className="text-xs font-medium text-gray-700 mb-2">Password Requirements:</p>
                  <div className="space-y-1">
                    <div className={`flex items-center text-xs ${
                      formData.password.length >= 8 ? 'text-green-600' : 'text-gray-500'
                    }`}>
                      <svg className={`w-3 h-3 mr-2 ${
                        formData.password.length >= 8 ? 'text-green-600' : 'text-gray-400'
                      }`} fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      At least 8 characters
                    </div>
                    <div className={`flex items-center text-xs ${
                      /[a-z]/.test(formData.password) ? 'text-green-600' : 'text-gray-500'
                    }`}>
                      <svg className={`w-3 h-3 mr-2 ${
                        /[a-z]/.test(formData.password) ? 'text-green-600' : 'text-gray-400'
                      }`} fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      1 lowercase letter (a-z)
                    </div>
                    <div className={`flex items-center text-xs ${
                      /[A-Z]/.test(formData.password) ? 'text-green-600' : 'text-gray-500'
                    }`}>
                      <svg className={`w-3 h-3 mr-2 ${
                        /[A-Z]/.test(formData.password) ? 'text-green-600' : 'text-gray-400'
                      }`} fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      1 uppercase letter (A-Z)
                    </div>
                    <div className={`flex items-center text-xs ${
                      /[0-9]/.test(formData.password) ? 'text-green-600' : 'text-gray-500'
                    }`}>
                      <svg className={`w-3 h-3 mr-2 ${
                        /[0-9]/.test(formData.password) ? 'text-green-600' : 'text-gray-400'
                      }`} fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      1 number (0-9)
                    </div>
                    <div className={`flex items-center text-xs ${
                      /[!@#$%^&*]/.test(formData.password) ? 'text-green-600' : 'text-gray-500'
                    }`}>
                      <svg className={`w-3 h-3 mr-2 ${
                        /[!@#$%^&*]/.test(formData.password) ? 'text-green-600' : 'text-gray-400'
                      }`} fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      1 special character (!@#$%^&*)
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* User Type Selection */}
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-3">Use for Business or Personal</h3>
              <div className="flex gap-4">
                <div className="flex-1">
                  <label
                    htmlFor="personalType"
                    className={`flex items-center justify-center block w-full py-3 px-4 rounded-md cursor-pointer transition ${
                      userType === "personal"
                        ? 'bg-blue-500 text-white'
                        : 'bg-gray-100 text-gray-700 border border-gray-300'
                    }`}
                  >
                    <input
                      type="radio"
                      id="personalType"
                      name="userType"
                      value="personal"
                      checked={userType === "personal"}
                      onChange={() => {
                        setUserType("personal");
                        // Clear business name error when switching to personal
                        if (errors.business_name) {
                          setErrors(prev => ({ ...prev, business_name: undefined }));
                        }
                        // Clear business name field when switching to personal
                        setFormData(prev => ({ ...prev, business_name: "" }));
                      }}
                      className="hidden"
                    />
                    Personal
                  </label>
                </div>
                <div className="flex-1">
                  <label
                    htmlFor="businessType"
                    className={`flex items-center justify-center block w-full py-3 px-4 rounded-md cursor-pointer transition ${
                      userType === "business"
                        ? 'bg-blue-500 text-white'
                        : 'bg-gray-100 text-gray-700 border border-gray-300'
                    }`}
                  >
                    <input
                      type="radio"
                      id="businessType"
                      name="userType"
                      value="business"
                      checked={userType === "business"}
                      onChange={() => setUserType("business")}
                      className="hidden"
                    />
                    Business
                  </label>
                </div>
              </div>
            </div>

            {/* Business Name - Only for Business Accounts */}
            {userType === "business" && (
              <div className="mb-6">
                <div className={`flex items-center border ${
                  errors.business_name ? 'border-red-500' : 'border-gray-300'
                } rounded-md`}>
                  <div className="pl-4 pr-2">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5 text-gray-700"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                      />
                    </svg>
                  </div>
                  <input
                    type="text"
                    name="business_name"
                    className="w-full py-3 px-2 text-gray-700 focus:outline-none"
                    placeholder="Business Name"
                    value={formData.business_name}
                    onChange={handleChange}
                  />
                </div>
                {errors.business_name && (
                  <p className="text-red-500 text-sm mt-1">{errors.business_name}</p>
                )}
              </div>
            )}

            {/* Terms and Conditions */}
            <div className="mb-6">
              <div className="flex items-start">
                <div className="flex items-center h-5">
                  <input
                    type="checkbox"
                    id="acceptTerms"
                    className="h-4 w-4 text-blue-600 rounded focus:ring-blue-500"
                    checked={acceptedTerms}
                    onChange={(e) => setAcceptedTerms(e.target.checked)}
                  />
                </div>
                <label
                  htmlFor="acceptTerms"
                  className="ml-2 text-sm text-gray-700"
                >
                  I accept the{' '}
                  <Link href="/terms" className="text-blue-600 hover:underline">
                    terms and conditions
                  </Link>
                </label>
              </div>
              {errors.terms && (
                <p className="text-red-500 text-sm mt-1">{errors.terms}</p>
              )}
            </div>

            {/* Submit Button */}
            <Button
              type="submit"
              className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-md font-semibold transition-colors text-xl"
              disabled={!acceptedTerms || isLoading}
            >
              {isLoading ? (
                <span className="flex items-center justify-center">
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Processing...
                </span>
              ) : (
                "Sign Up"
              )}
            </Button>
          </form>

          {/* Login Link */}
          <div className="text-center mt-6 poppins font-medium text-sm">
            <span className="text-gray-600">Already have an account?</span>
            <Link href="/login" className="text-blue-600 hover:underline ml-1">
              Login Now
            </Link>
          </div>
            </>
          )}
        </div>
      </div>

      {/* Right Side - Image */}
      <div className="hidden lg:block lg:w-1/2 relative overflow-hidden">
        <Image
          src="/assets/login-img.png"
          alt="Login Image"
          height={1000}
          width={1000}
          className="object-cover h-full w-full"
          priority
        />
      </div>
    </div>
  );
};

export default Register;