import { NextResponse } from "next/server";

export function middleware(req) {
    const token = req.cookies.get("accessToken")?.value; 
    const pathname = req.nextUrl.pathname;

    const publicPaths = ["/", "/about", "/contact", "/login", "/register", "/forgot-password"];
    const protectedPaths = ["/dashboard", "/profile"];
    const adminPaths = ["/admin"];

    if (!token && protectedPaths.includes(pathname)) {
        return NextResponse.redirect(new URL("/login", req.url));
    }

    if (token && ["/login", "/register", "/forgot-password"].includes(pathname)) {
        return NextResponse.redirect(new URL("/dashboard", req.url));
    }

    const userRole = req.cookies.get("role")?.value;

    if (adminPaths.includes(pathname) && userRole !== "admin") {
        return NextResponse.redirect(new URL("/dashboard", req.url));
    }

    return NextResponse.next();
}

export const config = {
    matcher: ["/dashboard", "/profile", "/admin", "/login", "/register", "/forgot-password"],
};
