"use client";

import { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';

const ProfileGuard = ({ children }) => {
  const [user, setUser] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();
  const pathname = usePathname();

  // Routes that don't require profile completion
  const allowedRoutes = [
    '/complete-profile',
    '/login',
    '/register',
    '/forgot-password',
    '/reset-password',
    '/',
    '/about',
    '/contact',
    '/privacy',
    '/terms'
  ];

  useEffect(() => {
    const checkUserProfile = () => {
      try {
        // Get user data from cookies
        const cookies = document.cookie.split(';');
        const userCookie = cookies.find(cookie => cookie.trim().startsWith('user='));

        if (userCookie) {
          const cookieValue = userCookie.split('=')[1];

          // Check if cookie value exists and is not empty
          if (cookieValue && cookieValue.trim() !== '' && cookieValue !== 'undefined') {
            const userData = JSON.parse(decodeURIComponent(cookieValue));
            setUser(userData);

            // Check if user profile is incomplete and not on allowed routes
            const isIncompleteProfile = !userData.first_name || !userData.last_name;
            const isAllowedRoute = allowedRoutes.some(route => pathname.startsWith(route));

            if (isIncompleteProfile && !isAllowedRoute) {
              // Redirect to complete profile page
              router.replace('/complete-profile');
              return;
            }

            // If user is on complete-profile page but profile is complete, redirect to dashboard
            if (!isIncompleteProfile && pathname === '/complete-profile') {
              router.replace('/dashboard');
              return;
            }
          } else {
            // Cookie exists but value is invalid, clear it
            document.cookie = 'user=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
            setUser(null);
          }
        } else {
          // No user cookie found
          setUser(null);
        }

        setIsLoading(false);
      } catch (error) {
        console.error('Error checking user profile:', error);
        // Clear invalid cookie on error
        document.cookie = 'user=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
        setUser(null);
        setIsLoading(false);
      }
    };

    checkUserProfile();
  }, [pathname, router]);

  // Show loading spinner while checking
  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // If user has incomplete profile and trying to access protected routes, show blocking message
  if (user && (!user.first_name || !user.last_name) && !allowedRoutes.some(route => pathname.startsWith(route))) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50">
        <div className="max-w-md mx-auto text-center p-8 bg-white rounded-lg shadow-lg">
          <div className="mb-6">
            <svg
              className="mx-auto h-16 w-16 text-yellow-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Profile Incomplete</h2>
          <p className="text-gray-600 mb-6">
            Please complete your profile information to continue using the platform.
          </p>
          <button
            onClick={() => router.push('/complete-profile')}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            Complete Profile Now
          </button>
        </div>
      </div>
    );
  }

  return children;
};

export default ProfileGuard;
