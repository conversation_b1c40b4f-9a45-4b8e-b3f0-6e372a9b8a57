/**
 * Navigation utilities for secure redirects without back button access
 */

/**
 * Redirects to a new page and prevents back button navigation
 * @param {string} url - The URL to redirect to
 * @param {boolean} clearHistory - Whether to clear browser history (default: true)
 */
export const secureRedirect = (url, clearHistory = true) => {
  if (typeof window === 'undefined') return;

  if (clearHistory) {
    // Replace current history entry to prevent back button
    window.location.replace(url);
  } else {
    // Normal navigation
    window.location.href = url;
  }
};

/**
 * Redirects after login/registration to prevent back navigation to auth pages
 * @param {string} url - The URL to redirect to
 * @param {string} message - Success message to show
 */
export const postAuthRedirect = (url, message = 'Success!') => {
  // Clear any auth-related history
  if (typeof window !== 'undefined') {
    // Replace the current entry in history
    window.history.replaceState(null, '', url);
    // Then navigate to ensure clean state
    window.location.replace(url);
  }
};

/**
 * Clears browser history and redirects (for logout scenarios)
 * @param {string} url - The URL to redirect to
 */
export const logoutRedirect = (url = '/login') => {
  if (typeof window === 'undefined') return;

  // Clear session storage
  sessionStorage.clear();
  
  // Replace current page to prevent back navigation
  window.location.replace(url);
};

/**
 * Checks if user can navigate back (for debugging)
 * @returns {boolean} - Whether back navigation is possible
 */
export const canNavigateBack = () => {
  if (typeof window === 'undefined') return false;
  return window.history.length > 1;
};
