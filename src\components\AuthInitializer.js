"use client";

import { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { initializeAuth } from '@/redux/slices/authSlice';
import { getCookie } from '@/utils/auth';

/**
 * Component to initialize auth state from cookies on app startup
 * This ensures Redux auth state is synced with existing cookies
 */
const AuthInitializer = () => {
  const dispatch = useDispatch();

  useEffect(() => {
    // Check for existing tokens in cookies
    const accessToken = getCookie('accessToken');
    const refreshToken = getCookie('refreshToken');

    if (accessToken) {
      // Initialize Redux auth state with existing tokens
      dispatch(initializeAuth({
        accessToken,
        refreshToken
      }));
    }
  }, [dispatch]);

  // This component doesn't render anything
  return null;
};

export default AuthInitializer;
