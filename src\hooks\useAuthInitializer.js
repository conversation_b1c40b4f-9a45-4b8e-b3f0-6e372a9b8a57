import { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { initializeAuth } from '@/redux/slices/authSlice';
import { getCookie } from '@/utils/auth';

/**
 * Hook to initialize auth state from cookies on app startup
 * This ensures Redux auth state is synced with existing cookies
 */
export const useAuthInitializer = () => {
  const dispatch = useDispatch();

  useEffect(() => {
    // Check for existing tokens in cookies
    const accessToken = getCookie('accessToken');
    const refreshToken = getCookie('refreshToken');

    if (accessToken) {
      // Initialize Redux auth state with existing tokens
      dispatch(initializeAuth({
        accessToken,
        refreshToken
      }));
    }
  }, [dispatch]);
};

export default useAuthInitializer;
