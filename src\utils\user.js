/**
 * Get the user object from cookies
 * @returns {Object|null} The user object or null if not found
 */
export const getUserFromCookies = () => {
  if (typeof document === 'undefined') return null;

  try {
    const userCookie = document.cookie
      .split('; ')
      .find(row => row.startsWith('user='));

    if (!userCookie) return null;

    const userJson = userCookie.split('=')[1];

    // Check if cookie value exists and is not empty or undefined
    if (!userJson || userJson.trim() === '' || userJson === 'undefined') {
      return null;
    }

    return JSON.parse(decodeURIComponent(userJson));
  } catch (error) {
    console.error('Error parsing user cookie:', error);
    return null;
  }
};

/**
 * Get the user role from cookies
 * @returns {string|null} The user role or null if not found
 */
export const getUserRole = () => {
  const user = getUserFromCookies();
  // Check different possible locations of role information
  return user?.roles?.[0] || user?.role || null;
};

/**
 * Check if the user is a seller
 * @returns {boolean} True if the user is a seller, false otherwise
 */
export const isSeller = () => {
  const role = getUserRole();
  return role === 'Seller' || role === 'seller';
};

/**
 * Check if the user is a buyer
 * @returns {boolean} True if the user is a buyer, false otherwise
 */
export const isBuyer = () => {
  const role = getUserRole();
  return role === 'Buyer' || role === 'buyer';
};

/**
 * Get the user's name from cookies
 * @returns {string} The user's name or "User" if not found
 */
export const getUserName = () => {
  const user = getUserFromCookies();
  if (!user) return "User";

  if (user.first_name && user.last_name) {
    return `${user.first_name} ${user.last_name}`;
  } else if (user.first_name) {
    return user.first_name;
  } else if (user.name) {
    return user.name;
  }

  return "User";
};

/**
 * Check if user is logged in
 * @returns {boolean} True if user is logged in
 */
export const isLoggedIn = () => {
  return !!getUserFromCookies();
};

/**
 * Get the subscription object from cookies
 * @returns {Object|null} The subscription object or null if not found
 */
export const getSubscriptionFromCookies = () => {
  if (typeof document === 'undefined') return null;

  try {
    const subscriptionCookie = document.cookie
      .split('; ')
      .find(row => row.startsWith('subscription='));

    if (!subscriptionCookie) return null;

    const subscriptionJson = subscriptionCookie.split('=')[1];

    // Check if cookie value exists and is not empty or undefined
    if (!subscriptionJson || subscriptionJson.trim() === '' || subscriptionJson === 'undefined') {
      return null;
    }

    return JSON.parse(decodeURIComponent(subscriptionJson));
  } catch (error) {
    console.error('Error parsing subscription cookie:', error);
    return null;
  }
};

/**
 * Check if user has an active subscription
 * @returns {boolean} True if user has an active subscription
 */
export const hasActiveSubscription = () => {
  const subscription = getSubscriptionFromCookies();
  return subscription?.status === 'ACTIVE';
};

/**
 * Check if user is on a trial plan
 * @returns {boolean} True if user is on a trial plan
 */
export const isOnTrialPlan = () => {
  const subscription = getSubscriptionFromCookies();
  return subscription?.plan?.features?.trial === true;
};

/**
 * Get subscription plan name
 * @returns {string} The subscription plan name or "No Plan" if not found
 */
export const getSubscriptionPlanName = () => {
  const subscription = getSubscriptionFromCookies();
  return subscription?.plan?.name || "No Plan";
};

/**
 * Get days remaining in subscription
 * @returns {number|null} Days remaining or null if no subscription
 */
export const getDaysRemaining = () => {
  const subscription = getSubscriptionFromCookies();
  if (!subscription?.end_date) return null;

  const endDate = new Date(subscription.end_date);
  const today = new Date();
  const diffTime = endDate - today;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  return diffDays > 0 ? diffDays : 0;
};

/**
 * Clear all cookies (for logout)
 */
export const clearAllCookies = () => {
  if (typeof document === 'undefined') {
    return;
  }

  // Clear specific cookies first
  document.cookie = "accessToken=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
  document.cookie = "refreshToken=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
  document.cookie = "user=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
  document.cookie = "subscription=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";

  // Clear all other cookies
  const cookies = document.cookie.split(';');

  for (let i = 0; i < cookies.length; i++) {
    const cookie = cookies[i];
    const eqPos = cookie.indexOf('=');
    const name = eqPos > -1 ? cookie.substring(0, eqPos).trim() : cookie.trim();
    document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;
  }
};
