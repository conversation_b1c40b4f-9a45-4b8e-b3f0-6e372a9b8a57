"use client";

import React, { useState, useEffect } from "react";
import { useMutateApiMutation } from "@/redux/services/api";
import { useDispatch } from 'react-redux';
import { logout } from '@/redux/slices/authSlice';
import Image from "next/image";
import Button from "@/components/backend/Button";
import { useRouter } from 'next/navigation';
import toast from 'react-hot-toast';

const CompleteProfile = () => {
  const [formData, setFormData] = useState({
    // Common fields
    first_name: "",
    last_name: "",
    phone_number: "",
    country: "",
    street: "",
    city: "",
    state: "",
    zip: "",
    profile_picture: null,

    // Personal user fields
    occupation: "",
    date_of_birth: "",
    gender: "",
    interests: "",
    bio: "",
    nid_document: null,

    // Business user fields
    business_name: "",
    business_type: "",
    business_website: "",
    business_registration_number: "",
    business_address: "",
    tax_id: "",
    business_document: null
  });
  const [errors, setErrors] = useState({});
  const [user, setUser] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  const router = useRouter();
  const dispatch = useDispatch();
  const [mutateApi, { data: mutationData, error: mutationError }] = useMutateApiMutation();

  // Get user data from cookies on component mount
  useEffect(() => {
    const getUserFromCookie = () => {
      const cookies = document.cookie.split(';');
      const userCookie = cookies.find(cookie => cookie.trim().startsWith('user='));
      if (userCookie) {
        try {
          const cookieValue = userCookie.split('=')[1];

          // Check if cookie value exists and is not empty or undefined
          if (!cookieValue || cookieValue.trim() === '' || cookieValue === 'undefined') {
            router.replace("/login");
            return;
          }

          const userData = JSON.parse(decodeURIComponent(cookieValue));
          setUser(userData);
          
          // Pre-fill form with existing user data
          setFormData(prev => ({
            ...prev,
            first_name: userData.first_name || "",
            last_name: userData.last_name || "",
            phone_number: userData.phone_number || "",
            country: userData.country || "",
            street: userData.street || "",
            city: userData.city || "",
            state: userData.state || "",
            zip: userData.zip || "",
            gender: userData.gender || "",
            occupation: userData.occupation || "",
            date_of_birth: userData.date_of_birth || "",
            interests: userData.interests || "",
            bio: userData.bio || "",
            business_name: userData.business_name || "",
            business_type: userData.business_type || "",
            business_website: userData.business_website || "",
            business_registration_number: userData.business_registration_number || "",
            business_address: userData.business_address || "",
            tax_id: userData.tax_id || ""
          }));
        } catch (error) {
          console.error("Error parsing user cookie:", error);
          router.replace("/login");
        }
      } else {
        router.replace("/login");
      }
    };

    getUserFromCookie();
  }, [router]);

  // Handle API response
  useEffect(() => {
    if (mutationData) {
      console.log("Profile Update Response:", mutationData);
      document.cookie = `user=${JSON.stringify(mutationData?.data?.user)};path=/;max-age=31536000`;

      // Store subscription data if available
      if (mutationData?.data?.subscription) {
        document.cookie = `subscription=${JSON.stringify(mutationData?.data?.subscription)};path=/;max-age=31536000`;
      }

      toast.success('Profile completed successfully!');
      // Use window.location.replace to prevent back button navigation
      window.location.replace("/dashboard");
    }
    if (mutationError) {
      console.log("Profile Update Error:", JSON.stringify(mutationError, null, 2));
      
      if (mutationError.data?.error) {
        const serverErrors = {};
        Object.keys(mutationError.data.error).forEach(field => {
          const fieldErrors = mutationError.data.error[field];
          if (Array.isArray(fieldErrors)) {
            serverErrors[field] = fieldErrors[0];
          } else {
            serverErrors[field] = fieldErrors;
          }
        });
        setErrors(serverErrors);
        
        const firstErrorKey = Object.keys(serverErrors)[0];
        if (firstErrorKey) {
          toast.error(serverErrors[firstErrorKey]);
        }
      } else if (mutationError.data?.message) {
        setErrors({ server: mutationError.data.message });
        toast.error(mutationError.data.message);
      } else {
        setErrors({ server: "An error occurred while updating profile. Please try again." });
        toast.error("An error occurred while updating profile. Please try again.");
      }
    }
  }, [mutationData, mutationError, router]);

  const validateForm = () => {
    let newErrors = {};

    // Common required fields
    const commonFields = ['first_name', 'last_name', 'phone_number', 'country', 'street', 'city', 'state', 'zip'];

    commonFields.forEach(field => {
      const error = validateField(field, formData[field]);
      if (error) {
        newErrors[field] = error;
      }
    });

    // Personal user specific validations
    if (user?.user_type === "personal") {
      const personalFields = ['occupation', 'date_of_birth', 'gender'];

      personalFields.forEach(field => {
        const error = validateField(field, formData[field]);
        if (error) {
          newErrors[field] = error;
        }
      });

      // File validation for NID document
      const nidError = validateFile('nid_document', formData.nid_document);
      if (nidError) {
        newErrors.nid_document = nidError;
      }

      // Optional field validations
      if (formData.interests && formData.interests.length > 1000) {
        newErrors.interests = "Interests must not exceed 1000 characters";
      }

      if (formData.bio && formData.bio.length > 1000) {
        newErrors.bio = "Bio must not exceed 1000 characters";
      }
    }

    // Business user specific validations
    if (user?.user_type === "business") {
      const businessFields = ['business_name', 'business_type', 'business_address'];

      businessFields.forEach(field => {
        const error = validateField(field, formData[field]);
        if (error) {
          newErrors[field] = error;
        }
      });

      // File validation for business document
      const businessDocError = validateFile('business_document', formData.business_document);
      if (businessDocError) {
        newErrors.business_document = businessDocError;
      }

      // Optional field validations
      if (formData.business_website) {
        const websiteError = validateField('business_website', formData.business_website);
        if (websiteError) {
          newErrors.business_website = websiteError;
        }
      }

      if (formData.business_registration_number && (formData.business_registration_number.length < 5 || formData.business_registration_number.length > 50)) {
        newErrors.business_registration_number = "Business registration number must be between 5 and 50 characters";
      }

      if (formData.tax_id && (formData.tax_id.length < 5 || formData.tax_id.length > 50)) {
        newErrors.tax_id = "Tax/VAT ID must be between 5 and 50 characters";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);

    if (validateForm()) {
      const profileData = new FormData();

      // Common fields
      profileData.append('first_name', formData.first_name.trim());
      profileData.append('last_name', formData.last_name.trim());
      profileData.append('phone_number', formData.phone_number.trim());
      profileData.append('country', formData.country.trim());
      profileData.append('street', formData.street.trim());
      profileData.append('city', formData.city.trim());
      profileData.append('state', formData.state.trim());
      profileData.append('zip', formData.zip.trim());
      profileData.append('user_type', user?.user_type); // Add user_type for validation

      if (formData.profile_picture) {
        profileData.append('profile_picture', formData.profile_picture);
      }

      // Personal user specific fields
      if (user?.user_type === "personal") {
        profileData.append('occupation', formData.occupation.trim());
        profileData.append('date_of_birth', formData.date_of_birth);
        profileData.append('gender', formData.gender);
        profileData.append('interests', formData.interests.trim());
        profileData.append('bio', formData.bio.trim());
        profileData.append('nid_document', formData.nid_document);
      }

      // Business user specific fields
      if (user?.user_type === "business") {
        profileData.append('business_name', formData.business_name.trim());
        profileData.append('business_type', formData.business_type);
        profileData.append('business_website', formData.business_website.trim());
        profileData.append('business_registration_number', formData.business_registration_number.trim());
        profileData.append('business_address', formData.business_address.trim());
        profileData.append('tax_id', formData.tax_id.trim());
        profileData.append('business_document', formData.business_document);
      }

      try {
        await mutateApi({
          endpoint: "/auth/complete-profile",
          data: profileData,
          isFormData: true
        });
      } catch (error) {
        console.log("Profile completion error:", JSON.stringify(error, null, 2));
      }
    }
    setIsLoading(false);
  };

  const validateField = (name, value) => {
    let error = '';

    switch (name) {
      case 'first_name':
        if (!value.trim()) {
          error = 'First name is required';
        } else if (value.trim().length < 2) {
          error = 'First name must be at least 2 characters';
        } else if (value.trim().length > 50) {
          error = 'First name must not exceed 50 characters';
        } else if (!/^[a-zA-Z\s]+$/.test(value.trim())) {
          error = 'First name can only contain letters and spaces';
        }
        break;

      case 'last_name':
        if (!value.trim()) {
          error = 'Last name is required';
        } else if (value.trim().length < 2) {
          error = 'Last name must be at least 2 characters';
        } else if (value.trim().length > 50) {
          error = 'Last name must not exceed 50 characters';
        } else if (!/^[a-zA-Z\s]+$/.test(value.trim())) {
          error = 'Last name can only contain letters and spaces';
        }
        break;

      case 'phone_number':
        if (!value.trim()) {
          error = 'Phone number is required';
        } else if (!/^[+]?[0-9\s\-\(\)]{10,15}$/.test(value)) {
          error = 'Please enter a valid phone number (10-15 digits)';
        }
        break;

      case 'email':
        if (!value.trim()) {
          error = 'Email is required';
        } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
          error = 'Please enter a valid email address';
        }
        break;

      case 'country':
        if (!value.trim()) {
          error = 'Country is required';
        } else if (value.trim().length < 2) {
          error = 'Country must be at least 2 characters';
        }
        break;

      case 'street':
        if (!value.trim()) {
          error = 'Street address is required';
        } else if (value.trim().length < 5) {
          error = 'Street address must be at least 5 characters';
        }
        break;

      case 'city':
        if (!value.trim()) {
          error = 'City is required';
        } else if (value.trim().length < 2) {
          error = 'City must be at least 2 characters';
        }
        break;

      case 'state':
        if (!value.trim()) {
          error = 'State/Province is required';
        } else if (value.trim().length < 2) {
          error = 'State/Province must be at least 2 characters';
        }
        break;

      case 'zip':
        if (!value.trim()) {
          error = 'ZIP/Postal code is required';
        } else if (value.trim().length < 3) {
          error = 'ZIP/Postal code must be at least 3 characters';
        }
        break;

      // Personal user validations
      case 'occupation':
        if (user?.user_type === "personal") {
          if (!value.trim()) {
            error = 'Occupation is required';
          } else if (value.trim().length < 2) {
            error = 'Occupation must be at least 2 characters';
          }
        }
        break;

      case 'date_of_birth':
        if (user?.user_type === "personal") {
          if (!value) {
            error = 'Date of birth is required';
          } else {
            const birthDate = new Date(value);
            const today = new Date();
            const age = today.getFullYear() - birthDate.getFullYear();
            if (age < 13) {
              error = 'You must be at least 13 years old';
            } else if (age > 120) {
              error = 'Please enter a valid date of birth';
            }
          }
        }
        break;

      case 'gender':
        if (user?.user_type === "personal" && !value) {
          error = 'Gender is required';
        }
        break;

      // Business user validations
      case 'business_name':
        if (user?.user_type === "business") {
          if (!value.trim()) {
            error = 'Business name is required';
          } else if (value.trim().length < 2) {
            error = 'Business name must be at least 2 characters';
          }
        }
        break;

      case 'business_type':
        if (user?.user_type === "business" && !value) {
          error = 'Business type is required';
        }
        break;

      case 'business_address':
        if (user?.user_type === "business") {
          if (!value.trim()) {
            error = 'Business address is required';
          } else if (value.trim().length < 10) {
            error = 'Business address must be at least 10 characters';
          }
        }
        break;

      case 'business_website':
        if (value && !/^https?:\/\/.+\..+/.test(value)) {
          error = 'Please enter a valid website URL (e.g., https://example.com)';
        }
        break;

      default:
        break;
    }

    return error;
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Real-time validation
    const fieldError = validateField(name, value);
    setErrors(prev => ({
      ...prev,
      [name]: fieldError,
      server: undefined // Clear server error when user starts typing
    }));
  };

  const validateFile = (name, file) => {
    let error = '';

    if (!file) {
      if (name === 'nid_document' && user?.user_type === "personal") {
        error = 'National ID document is required';
      } else if (name === 'business_document' && user?.user_type === "business") {
        error = 'Business document is required';
      }
      return error;
    }

    // File size validation (max 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      error = 'File size must be less than 5MB';
      return error;
    }

    // File type validation
    const allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'];
    if (!allowedTypes.includes(file.type)) {
      error = 'Only PDF, JPG, JPEG, and PNG files are allowed';
      return error;
    }

    return error;
  };

  const handleFileChange = (e) => {
    const { name, files } = e.target;
    const file = files && files[0];

    setFormData(prev => ({ ...prev, [name]: file }));

    // Validate file
    const fileError = validateFile(name, file);
    setErrors(prev => ({
      ...prev,
      [name]: fileError,
      server: undefined
    }));
  };

  const handleLogout = () => {
    // Clear Redux auth state
    dispatch(logout());

    // Clear user and subscription cookies (keep tokens in Redux only)
    document.cookie = "user=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
    document.cookie = "subscription=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
    document.cookie = "role=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";

    // Clear localStorage and sessionStorage
    localStorage.clear();
    sessionStorage.clear();

    toast.success('Logged out successfully');

    // Redirect to login using window.location.replace to prevent back button
    window.location.replace("/login");
  };

  // Prevent navigation away from this page
  useEffect(() => {
    const handleBeforeUnload = (e) => {
      e.preventDefault();
      e.returnValue = '';
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, []);

  if (!user) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-6xl mx-auto">
        <div className="w-full">
          {/* Header with Logo and Logout */}
          <div className="mb-8 flex justify-between items-start">
            <div>
              <Image
                src="/assets/backend_assets/images/site-logo.svg"
                alt="logo"
                width={100}
                height={100}
                className="w-[200px]"
                priority
              />
            </div>
            <button
              onClick={handleLogout}
              className="flex items-center gap-2 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
                />
              </svg>
              Logout
            </button>
          </div>

          <h2 className="text-4xl inter font-bold text-gray-800 mb-2">
            Complete Your Profile
          </h2>
          <p className="text-gray-600 mt-4 mb-6 poppins">
            Please provide the following information to complete your account setup.
          </p>

          {/* Warning Message */}
          <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 text-yellow-800 rounded-md flex items-start">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 mr-2 mt-0.5 flex-shrink-0"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                clipRule="evenodd"
              />
            </svg>
            <span>Your profile is incomplete. Please fill in all required information to continue using the platform.</span>
          </div>

          {/* Server Error Message */}
          {errors.server && (
            <div className="mb-6 p-4 bg-red-50 border border-red-200 text-red-700 rounded-md flex items-start">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 mr-2 mt-0.5 flex-shrink-0"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                  clipRule="evenodd"
                />
              </svg>
              <span>{errors.server}</span>
            </div>
          )}

          <form onSubmit={handleSubmit} noValidate>
            {/* User Type Display */}
            <div className="mb-8 p-4 bg-blue-50 border border-blue-200 rounded-md">
              <div className="flex items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5 text-blue-600 mr-2"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                <span className="text-blue-800 font-medium">
                  Account Type: {user?.user_type === "personal" ? "Personal Account" : "Business Account"}
                </span>
              </div>
            </div>

            {/* Two Column Layout */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">

                {/* First Name */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    First Name <span className="text-red-500">*</span>
                  </label>
                  <div className={`flex items-center border ${
                    errors.first_name ? 'border-red-500' : 'border-gray-300'
                  } rounded-md focus-within:border-blue-500 focus-within:ring-1 focus-within:ring-blue-500`}>
                    <div className="pl-4 pr-2">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5 text-gray-700"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                        />
                      </svg>
                    </div>
                    <input
                      type="text"
                      name="first_name"
                      className="w-full py-3 px-2 text-gray-700 focus:outline-none"
                      placeholder="Enter your first name"
                      value={formData.first_name}
                      onChange={handleChange}
                    />
                  </div>
                  {errors.first_name && (
                    <p className="text-red-500 text-sm mt-1 flex items-center">
                      <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                      {errors.first_name}
                    </p>
                  )}
                </div>

                {/* Last Name */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Last Name <span className="text-red-500">*</span>
                  </label>
                  <div className={`flex items-center border ${
                    errors.last_name ? 'border-red-500' : 'border-gray-300'
                  } rounded-md focus-within:border-blue-500 focus-within:ring-1 focus-within:ring-blue-500`}>
                    <div className="pl-4 pr-2">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5 text-gray-700"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                        />
                      </svg>
                    </div>
                    <input
                      type="text"
                      name="last_name"
                      className="w-full py-3 px-2 text-gray-700 focus:outline-none"
                      placeholder="Enter your last name"
                      value={formData.last_name}
                      onChange={handleChange}
                    />
                  </div>
                  {errors.last_name && (
                    <p className="text-red-500 text-sm mt-1 flex items-center">
                      <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                      {errors.last_name}
                    </p>
                  )}
                </div>

                {/* Phone Number */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Phone Number <span className="text-red-500">*</span>
                  </label>
                  <div className={`flex items-center border ${
                    errors.phone_number ? 'border-red-500' : 'border-gray-300'
                  } rounded-md focus-within:border-blue-500 focus-within:ring-1 focus-within:ring-blue-500`}>
                    <div className="pl-4 pr-2">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5 text-gray-700"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                        />
                      </svg>
                    </div>
                    <input
                      type="tel"
                      name="phone_number"
                      className="w-full py-3 px-2 text-gray-700 focus:outline-none"
                      placeholder="Enter your phone number"
                      value={formData.phone_number}
                      onChange={handleChange}
                    />
                  </div>
                  {errors.phone_number && (
                    <p className="text-red-500 text-sm mt-1 flex items-center">
                      <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                      {errors.phone_number}
                    </p>
                  )}
                </div>

                {/* Street Address */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Street Address <span className="text-red-500">*</span>
                  </label>
                  <div className={`flex items-center border ${
                    errors.street ? 'border-red-500' : 'border-gray-300'
                  } rounded-md focus-within:border-blue-500 focus-within:ring-1 focus-within:ring-blue-500`}>
                    <div className="pl-4 pr-2">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5 text-gray-700"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                        />
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                        />
                      </svg>
                    </div>
                    <input
                      type="text"
                      name="street"
                      className="w-full py-3 px-2 text-gray-700 focus:outline-none"
                      placeholder="Enter your street address"
                      value={formData.street}
                      onChange={handleChange}
                    />
                  </div>
                  {errors.street && (
                    <p className="text-red-500 text-sm mt-1 flex items-center">
                      <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                      {errors.street}
                    </p>
                  )}
                </div>

                {/* State */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    State/Province <span className="text-red-500">*</span>
                  </label>
                  <div className={`flex items-center border ${
                    errors.state ? 'border-red-500' : 'border-gray-300'
                  } rounded-md focus-within:border-blue-500 focus-within:ring-1 focus-within:ring-blue-500`}>
                    <div className="pl-4 pr-2">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5 text-gray-700"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"
                        />
                      </svg>
                    </div>
                    <input
                      type="text"
                      name="state"
                      className="w-full py-3 px-2 text-gray-700 focus:outline-none"
                      placeholder="Enter your state or province"
                      value={formData.state}
                      onChange={handleChange}
                    />
                  </div>
                  {errors.state && (
                    <p className="text-red-500 text-sm mt-1 flex items-center">
                      <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                      {errors.state}
                    </p>
                  )}
                </div>

                {/* Country */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Country <span className="text-red-500">*</span>
                  </label>
                  <div className={`flex items-center border ${
                    errors.country ? 'border-red-500' : 'border-gray-300'
                  } rounded-md focus-within:border-blue-500 focus-within:ring-1 focus-within:ring-blue-500`}>
                    <div className="pl-4 pr-2">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5 text-gray-700"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>
                    </div>
                    <input
                      type="text"
                      name="country"
                      className="w-full py-3 px-2 text-gray-700 focus:outline-none"
                      placeholder="Enter your country"
                      value={formData.country}
                      onChange={handleChange}
                    />
                  </div>
                  {errors.country && (
                    <p className="text-red-500 text-sm mt-1 flex items-center">
                      <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                      {errors.country}
                    </p>
                  )}
                </div>

                {/* City */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    City <span className="text-red-500">*</span>
                  </label>
                  <div className={`flex items-center border ${
                    errors.city ? 'border-red-500' : 'border-gray-300'
                  } rounded-md focus-within:border-blue-500 focus-within:ring-1 focus-within:ring-blue-500`}>
                    <div className="pl-4 pr-2">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5 text-gray-700"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                        />
                      </svg>
                    </div>
                    <input
                      type="text"
                      name="city"
                      className="w-full py-3 px-2 text-gray-700 focus:outline-none"
                      placeholder="Enter your city"
                      value={formData.city}
                      onChange={handleChange}
                    />
                  </div>
                  {errors.city && (
                    <p className="text-red-500 text-sm mt-1 flex items-center">
                      <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                      {errors.city}
                    </p>
                  )}
                </div>

                {/* ZIP/Postal Code */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    ZIP/Postal Code <span className="text-red-500">*</span>
                  </label>
                  <div className={`flex items-center border ${
                    errors.zip ? 'border-red-500' : 'border-gray-300'
                  } rounded-md focus-within:border-blue-500 focus-within:ring-1 focus-within:ring-blue-500`}>
                    <div className="pl-4 pr-2">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5 text-gray-700"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"
                        />
                      </svg>
                    </div>
                    <input
                      type="text"
                      name="zip"
                      className="w-full py-3 px-2 text-gray-700 focus:outline-none"
                      placeholder="Enter your ZIP or postal code"
                      value={formData.zip}
                      onChange={handleChange}
                    />
                  </div>
                  {errors.zip && (
                    <p className="text-red-500 text-sm mt-1 flex items-center">
                      <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                      {errors.zip}
                    </p>
                  )}
                </div>
            </div>

            {/* Profile Picture (Optional) - Full Width */}
            <div className="my-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Profile Picture (Optional)
              </label>
              <div className={`flex items-center border ${
                errors.profile_picture ? 'border-red-500' : 'border-gray-300'
              } rounded-md focus-within:border-blue-500 focus-within:ring-1 focus-within:ring-blue-500`}>
                <div className="pl-4 pr-2">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5 text-gray-700"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                    />
                  </svg>
                </div>
                <input
                  type="file"
                  name="profile_picture"
                  className="w-full py-3 px-2 text-gray-700 focus:outline-none"
                  accept="image/*"
                  onChange={handleFileChange}
                />
              </div>
              {errors.profile_picture && (
                <p className="text-red-500 text-sm mt-1 flex items-center">
                  <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  {errors.profile_picture}
                </p>
              )}
            </div>

            {/* Conditional Fields Based on User Type */}
            {user?.user_type === "personal" && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mt-8">
                {/* Left Column - Personal Fields */}
                <div className="space-y-4">
                  {/* Occupation */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Occupation <span className="text-red-500">*</span>
                    </label>
                    <div className={`flex items-center border ${
                      errors.occupation ? 'border-red-500' : 'border-gray-300'
                    } rounded-md focus-within:border-blue-500 focus-within:ring-1 focus-within:ring-blue-500`}>
                      <div className="pl-4 pr-2">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-5 w-5 text-gray-700"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0H8m8 0v2a2 2 0 01-2 2H10a2 2 0 01-2-2V6"
                          />
                        </svg>
                      </div>
                      <input
                        type="text"
                        name="occupation"
                        className="w-full py-3 px-2 text-gray-700 focus:outline-none"
                        placeholder="Enter your occupation"
                        value={formData.occupation}
                        onChange={handleChange}
                      />
                    </div>
                    {errors.occupation && (
                      <p className="text-red-500 text-sm mt-1 flex items-center">
                        <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                        {errors.occupation}
                      </p>
                    )}
                  </div>

                  {/* Gender */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Gender <span className="text-red-500">*</span>
                    </label>
                    <div className={`flex items-center border ${
                      errors.gender ? 'border-red-500' : 'border-gray-300'
                    } rounded-md focus-within:border-blue-500 focus-within:ring-1 focus-within:ring-blue-500`}>
                      <div className="pl-4 pr-2">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-5 w-5 text-gray-700"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                          />
                        </svg>
                      </div>
                      <select
                        name="gender"
                        className="w-full py-3 px-2 text-gray-700 focus:outline-none"
                        value={formData.gender}
                        onChange={handleChange}
                      >
                        <option value="">Select your gender</option>
                        <option value="male">Male</option>
                        <option value="female">Female</option>
                        <option value="other">Other</option>
                      </select>
                    </div>
                    {errors.gender && (
                      <p className="text-red-500 text-sm mt-1 flex items-center">
                        <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                        {errors.gender}
                      </p>
                    )}
                  </div>

                  {/* Interests or Skills (Optional) */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Interests or Skills (Optional)
                    </label>
                    <div className={`flex items-start border ${
                      errors.interests ? 'border-red-500' : 'border-gray-300'
                    } rounded-md`}>
                      <div className="pl-4 pr-2 pt-3">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-5 w-5 text-gray-700"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
                          />
                        </svg>
                      </div>
                      <textarea
                        name="interests"
                        className="w-full py-3 px-2 text-gray-700 focus:outline-none resize-none"
                        placeholder="Tell us about your interests or skills..."
                        rows="3"
                        value={formData.interests}
                        onChange={handleChange}
                      />
                    </div>
                    {errors.interests && (
                      <p className="text-red-500 text-sm mt-1">{errors.interests}</p>
                    )}
                  </div>
                </div>

                {/* Right Column - Personal Fields */}
                <div className="space-y-4">
                  {/* Date of Birth */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Date of Birth <span className="text-red-500">*</span>
                    </label>
                    <div className={`flex items-center border ${
                      errors.date_of_birth ? 'border-red-500' : 'border-gray-300'
                    } rounded-md focus-within:border-blue-500 focus-within:ring-1 focus-within:ring-blue-500`}>
                      <div className="pl-4 pr-2">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-5 w-5 text-gray-700"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                          />
                        </svg>
                      </div>
                      <input
                        type="date"
                        name="date_of_birth"
                        className="w-full py-3 px-2 text-gray-700 focus:outline-none"
                        value={formData.date_of_birth}
                        onChange={handleChange}
                      />
                    </div>
                    {errors.date_of_birth && (
                      <p className="text-red-500 text-sm mt-1 flex items-center">
                        <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                        {errors.date_of_birth}
                      </p>
                    )}
                  </div>

                  {/* Short Bio/About Me (Optional) */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Short Bio/About Me (Optional)
                    </label>
                    <div className={`flex items-start border ${
                      errors.bio ? 'border-red-500' : 'border-gray-300'
                    } rounded-md`}>
                      <div className="pl-4 pr-2 pt-3">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-5 w-5 text-gray-700"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                          />
                        </svg>
                      </div>
                      <textarea
                        name="bio"
                        className="w-full py-3 px-2 text-gray-700 focus:outline-none resize-none"
                        placeholder="Tell us about yourself..."
                        rows="4"
                        value={formData.bio}
                        onChange={handleChange}
                      />
                    </div>
                    {errors.bio && (
                      <p className="text-red-500 text-sm mt-1">{errors.bio}</p>
                    )}
                  </div>

                  {/* Upload National ID (NID) Document */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Upload National ID (NID) Document <span className="text-red-500">*</span>
                    </label>
                    <div className={`flex items-center border ${
                      errors.nid_document ? 'border-red-500' : 'border-gray-300'
                    } rounded-md focus-within:border-blue-500 focus-within:ring-1 focus-within:ring-blue-500`}>
                      <div className="pl-4 pr-2">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-5 w-5 text-gray-700"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                          />
                        </svg>
                      </div>
                      <input
                        type="file"
                        name="nid_document"
                        className="w-full py-3 px-2 text-gray-700 focus:outline-none"
                        accept=".pdf,.jpg,.jpeg,.png"
                        onChange={handleFileChange}
                      />
                    </div>
                    <p className="text-xs text-gray-500 mt-1">Accepted formats: PDF, JPG, JPEG, PNG</p>
                    {errors.nid_document && (
                      <p className="text-red-500 text-sm mt-1 flex items-center">
                        <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                        {errors.nid_document}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Business User Fields */}
            {user?.user_type === "business" && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mt-8">
                {/* Left Column - Business Fields */}
                <div className="space-y-4">
                  {/* Business Name */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Business Name <span className="text-red-500">*</span>
                    </label>
                    <div className={`flex items-center border ${
                      errors.business_name ? 'border-red-500' : 'border-gray-300'
                    } rounded-md focus-within:border-blue-500 focus-within:ring-1 focus-within:ring-blue-500`}>
                      <div className="pl-4 pr-2">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-5 w-5 text-gray-700"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                          />
                        </svg>
                      </div>
                      <input
                        type="text"
                        name="business_name"
                        className="w-full py-3 px-2 text-gray-700 focus:outline-none"
                        placeholder="Enter your business name"
                        value={formData.business_name}
                        onChange={handleChange}
                      />
                    </div>
                    {errors.business_name && (
                      <p className="text-red-500 text-sm mt-1 flex items-center">
                        <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                        {errors.business_name}
                      </p>
                    )}
                  </div>

                  {/* Business Type */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Business Type <span className="text-red-500">*</span>
                    </label>
                    <div className={`flex items-center border ${
                      errors.business_type ? 'border-red-500' : 'border-gray-300'
                    } rounded-md focus-within:border-blue-500 focus-within:ring-1 focus-within:ring-blue-500`}>
                      <div className="pl-4 pr-2">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-5 w-5 text-gray-700"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                          />
                        </svg>
                      </div>
                      <select
                        name="business_type"
                        className="w-full py-3 px-2 text-gray-700 focus:outline-none"
                        value={formData.business_type}
                        onChange={handleChange}
                      >
                        <option value="">Select your business type</option>
                        <option value="IT">IT</option>
                        <option value="Clothing">Clothing</option>
                        <option value="Restaurant">Restaurant</option>
                        <option value="Retail">Retail</option>
                        <option value="Service">Service</option>
                        <option value="Other">Other</option>
                      </select>
                    </div>
                    {errors.business_type && (
                      <p className="text-red-500 text-sm mt-1 flex items-center">
                        <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                        {errors.business_type}
                      </p>
                    )}
                  </div>

                  {/* Business Website (Optional) */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Business Website (Optional)
                    </label>
                    <div className={`flex items-center border ${
                      errors.business_website ? 'border-red-500' : 'border-gray-300'
                    } rounded-md`}>
                      <div className="pl-4 pr-2">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-5 w-5 text-gray-700"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"
                          />
                        </svg>
                      </div>
                      <input
                        type="url"
                        name="business_website"
                        className="w-full py-3 px-2 text-gray-700 focus:outline-none"
                        placeholder="https://www.example.com"
                        value={formData.business_website}
                        onChange={handleChange}
                      />
                    </div>
                    {errors.business_website && (
                      <p className="text-red-500 text-sm mt-1">{errors.business_website}</p>
                    )}
                  </div>
                </div>

                {/* Right Column - Business Fields */}
                <div className="space-y-4">
                  {/* Business Registration Number (Optional) */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Business Registration Number (Optional)
                    </label>
                    <div className={`flex items-center border ${
                      errors.business_registration_number ? 'border-red-500' : 'border-gray-300'
                    } rounded-md`}>
                      <div className="pl-4 pr-2">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-5 w-5 text-gray-700"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                          />
                        </svg>
                      </div>
                      <input
                        type="text"
                        name="business_registration_number"
                        className="w-full py-3 px-2 text-gray-700 focus:outline-none"
                        placeholder="Business Registration Number"
                        value={formData.business_registration_number}
                        onChange={handleChange}
                      />
                    </div>
                    {errors.business_registration_number && (
                      <p className="text-red-500 text-sm mt-1">{errors.business_registration_number}</p>
                    )}
                  </div>

                  {/* Business Address */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Business Address <span className="text-red-500">*</span>
                    </label>
                    <div className={`flex items-start border ${
                      errors.business_address ? 'border-red-500' : 'border-gray-300'
                    } rounded-md focus-within:border-blue-500 focus-within:ring-1 focus-within:ring-blue-500`}>
                      <div className="pl-4 pr-2 pt-3">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-5 w-5 text-gray-700"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                          />
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                          />
                        </svg>
                      </div>
                      <textarea
                        name="business_address"
                        className="w-full py-3 px-2 text-gray-700 focus:outline-none resize-none"
                        placeholder="Enter your complete business address"
                        rows="3"
                        value={formData.business_address}
                        onChange={handleChange}
                      />
                    </div>
                    {errors.business_address && (
                      <p className="text-red-500 text-sm mt-1 flex items-center">
                        <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                        {errors.business_address}
                      </p>
                    )}
                  </div>

                  {/* Tax/VAT ID (Optional) */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Tax/VAT ID (Optional)
                    </label>
                    <div className={`flex items-center border ${
                      errors.tax_id ? 'border-red-500' : 'border-gray-300'
                    } rounded-md`}>
                      <div className="pl-4 pr-2">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-5 w-5 text-gray-700"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"
                          />
                        </svg>
                      </div>
                      <input
                        type="text"
                        name="tax_id"
                        className="w-full py-3 px-2 text-gray-700 focus:outline-none"
                        placeholder="Tax/VAT ID"
                        value={formData.tax_id}
                        onChange={handleChange}
                      />
                    </div>
                    {errors.tax_id && (
                      <p className="text-red-500 text-sm mt-1">{errors.tax_id}</p>
                    )}
                  </div>

                  {/* Upload Business Document */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Upload Business Document <span className="text-red-500">*</span>
                    </label>
                    <div className={`flex items-center border ${
                      errors.business_document ? 'border-red-500' : 'border-gray-300'
                    } rounded-md focus-within:border-blue-500 focus-within:ring-1 focus-within:ring-blue-500`}>
                      <div className="pl-4 pr-2">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-5 w-5 text-gray-700"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                          />
                        </svg>
                      </div>
                      <input
                        type="file"
                        name="business_document"
                        className="w-full py-3 px-2 text-gray-700 focus:outline-none"
                        accept=".pdf,.jpg,.jpeg,.png"
                        onChange={handleFileChange}
                      />
                    </div>
                    <p className="text-xs text-gray-500 mt-1">Accepted formats: PDF, JPG, JPEG, PNG</p>
                    {errors.business_document && (
                      <p className="text-red-500 text-sm mt-1 flex items-center">
                        <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                        {errors.business_document}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Submit Button */}
            <div className="mt-8">
            <Button
              type="submit"
              className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              disabled={isLoading}
            >
              {isLoading ? (
                <div className="flex items-center justify-center">
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Completing Profile...
                </div>
              ) : (
                "Complete Profile"
              )}
            </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default CompleteProfile;
